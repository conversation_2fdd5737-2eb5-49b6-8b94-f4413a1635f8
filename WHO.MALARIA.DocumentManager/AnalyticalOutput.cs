﻿using ClosedXML.Excel;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.DocumentManager
{
    /// <summary>
    /// Contains methods for analytical output document(s) to create/process documents 
    /// </summary>
    public class AnalyticalOutput : IAnalyticalOutput
    {
        private readonly AnalyticalOutputExcelSetting _analyticalOutputExcelSetting;
        private readonly ITranslationService _translationService;

        public AnalyticalOutput(IOptions<AnalyticalOutputExcelSetting> analyticalOutputExcelSetting,
            ITranslationService translationService)
        {
            _analyticalOutputExcelSetting = analyticalOutputExcelSetting.Value;
            _translationService = translationService;
        }

        #region  Public Method

        /// <summary>
        /// Add chart and tabular data in a template, delete the sheets that are not modified
        /// </summary>
        /// <param name="chartData">Collection of chart data that must contain horizontal axis labels and series data along with the indicator sequence</param>
        ///  <param name="tabularData">Collection of tabular data</param>
        /// <returns>byte[] of the modified template</returns>
        public byte[] AddDataInTemplate(IEnumerable<ChartInputModel> chartData, IEnumerable<TabularDataInputModel> tabularData)
        {
            string language = _translationService.GetCurrentCulture();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                string templateFilePath = Path.Combine(Environment.CurrentDirectory, $"{Constants.AnalyticalOutputTemplateFilePath}_{language}.xlsx");

                using (XLWorkbook workbook = new XLWorkbook(templateFilePath))
                {
                    foreach (ChartInputModel data in chartData)
                    {
                        IXLWorksheet indicator_Worksheet = workbook.Worksheet(data.Name);

                        AddChartDataToWorksheet(indicator_Worksheet, data);
                    }

                    string[] sheetNames = chartData.Select(a => a.Name).ToArray();

                    DeleteWorksheets(workbook, sheetNames);

                    AddTabularDataInWorksheet(workbook, tabularData);

                    workbook.SaveAs(memoryStream);
                }

                return memoryStream.ToArray();
            }
        }

        #endregion

        #region Private Method           

        /// <summary>
        /// Add tabular data in worksheet
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="tabularData">Collection of tabular data</param>
        private void AddTabularDataInWorksheet(XLWorkbook workbook, IEnumerable<TabularDataInputModel> tabularData)
        {
            foreach (TabularDataInputModel data in tabularData)
            {
                switch (data.SheetName)
                {
                    case "2.1.2":
                        AddInteractiveTabularDataInWorksheet_2_1_2(workbook, data);
                        break;
                    default:

                        if (!workbook.Worksheets.Any(x => x.Name == data.SheetName))
                        {
                            IXLWorksheet worksheet = workbook.Worksheets.Add(data.SheetName);

                            int rowNumber = 1;
                            int columnNumber = 1;

                            foreach (DataTable dataTable in data.Tables.Tables)
                            {
                                worksheet.Cell(rowNumber, columnNumber).InsertTable(dataTable.AsEnumerable());

                                worksheet.Cells().Style.Alignment.WrapText = true;

                                worksheet.Columns().AdjustToContents();

                                // Adding additional rows, so that on Ul it shows the gaps(Space) between each data tables (tabular structure)
                                rowNumber = dataTable.Rows.Count + 3;
                            }
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// Delete work sheet that are not modified
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="sheetNames">List of sheet names</param>
        private void DeleteWorksheets(XLWorkbook workbook, string[] sheetNames)
        {
            string[] allSheetNames = _analyticalOutputExcelSetting.IndicatorsIncludedInTheTemplate;

            string[] sheetNamesToDelete = allSheetNames.Except(sheetNames).ToArray();

            foreach (string sheetName in sheetNamesToDelete)
            {
                IXLWorksheet worksheet = workbook.Worksheet(sheetName);

                worksheet.Delete();
            }
        }

        /// <summary>
        /// Add chart data to worksheet
        /// </summary>
        /// <param name="worksheet">An object of IXLWorksheet</param>
        ///<param name="chartData">Must contain horizontal axis label and series data</param>
        private void AddChartDataToWorksheet(IXLWorksheet worksheet, ChartInputModel chartData)
        {
            int rowNumber = _analyticalOutputExcelSetting.ChartData.FirstCellRowNumber;

            int columnNumber = _analyticalOutputExcelSetting.ChartData.FirstCellColumnNumber;

            string titleCell = _analyticalOutputExcelSetting.ChartData.TitleCellNumber;

            worksheet.Cell(titleCell).SetValue<string>(chartData.Title).Style.Protection.SetHidden(true).Protection.SetLocked(true);

            worksheet.Cell(rowNumber, columnNumber).Value = chartData.HorizontalAxisLabels;

            columnNumber++;
            int i = 0;
            foreach (dynamic series in chartData.Serieses)
            {
                worksheet.Cell(rowNumber, columnNumber).Value = series;
                if (chartData.TableTitles != null && chartData.TableTitles.Count > 0 && !string.IsNullOrWhiteSpace(chartData.TableTitles[i]))
                {
                    worksheet.Cell(rowNumber - 1, columnNumber).Value = chartData.TableTitles[i];
                }
                i++;
                columnNumber++;
            }
        }

        private void AddInteractiveTabularDataInWorksheet_2_1_2(XLWorkbook workbook, TabularDataInputModel tabularData)
        {
            IXLWorksheet worksheet = workbook.Worksheets.Add(tabularData.SheetName);
            int rowNumber = 1;
            int columnNumber = 1;

            foreach (DataTable dataTable in tabularData.Tables.Tables)
            {
                worksheet.Cell(rowNumber, columnNumber).InsertTable(dataTable.AsEnumerable());

                worksheet.Cells().Style.Alignment.WrapText = true;

                worksheet.Columns().AdjustToContents();
                for (int i = 0; i < dataTable.Rows.Count; i++)
                {
                    if (dataTable.Columns.Contains("Health Sectors"))
                    {
                        if (dataTable.Rows[i]["Health Sectors"].ToString().Contains("<b>"))
                        {
                            worksheet.Cell("A" + (i + 2)).Value = Regex.Replace(dataTable.Rows[i]["Health Sectors"].ToString(), "<.*?>", String.Empty);
                            worksheet.Cells("A" + (i + 2)).Style.Font.Bold = true;
                        }
                        if (dataTable.Rows[i]["Health Sectors"].ToString().Contains("&nbsp;"))
                        {
                            worksheet.Cell("A" + (i + 2)).Value = "     " + dataTable.Rows[i]["Health Sectors"].ToString().Replace("&nbsp;", "");
                        }
                    }
                    if (dataTable.Columns.Contains("Secteurs de la santé"))
                    {
                        if (dataTable.Rows[i]["Secteurs de la santé"].ToString().Contains("<b>"))
                        {
                            worksheet.Cell("A" + (i + 2)).Value = Regex.Replace(dataTable.Rows[i]["Secteurs de la santé"].ToString(), "<.*?>", String.Empty);
                            worksheet.Cells("A" + (i + 2)).Style.Font.Bold = true;
                        }
                        if (dataTable.Rows[i]["Secteurs de la santé"].ToString().Contains("&nbsp;"))
                        {
                            worksheet.Cell("A" + (i + 2)).Value = "     " + dataTable.Rows[i]["Secteurs de la santé"].ToString().Replace("&nbsp;", "");
                        }
                    }
                }
                // Adding additional rows, so that on Ul it shows the gaps(Space) between each data tables (tabular structure)
                rowNumber = dataTable.Rows.Count + 3;
            }
        }

        #endregion
    }
}
