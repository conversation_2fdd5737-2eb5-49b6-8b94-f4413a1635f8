import { disputedData } from "./disputedMapdata";

// Helper function to determine if coordinates are in Web Mercator format
function isWebMercator([x, y]: [number, number]): boolean {
  // Web Mercator coordinates are typically much larger than longitude/latitude
  // Longitude should be between -180 and 180, latitude between -90 and 90
  return Math.abs(x) > 180 || Math.abs(y) > 90;
}

function webMercatorToLonLat([x, y]: [number, number]): [number, number] {
  const RADIUS = 6378137;

  const lon = (x / RADIUS) * (180 / Math.PI);
  const lat = (y / RADIUS) * (180 / Math.PI);
  return [
    lon,
    (180 / Math.PI) * (2 * Math.atan(Math.exp(y / RADIUS)) - Math.PI / 2),
  ];
}

// Create GeoJSON features from disputedData
export const createDisputedFeatures = () => {
  const features: any = {
    type: "FeatureCollection",
    features: [],
  };

  Object.entries(disputedData).forEach(([name, regionData]: any) => {
    if (regionData.rings) {
      regionData.rings.forEach((ring: any) => {
        // Only convert if coordinates are in Web Mercator format
        const geographicRing = ring.map((coord: [number, number]) =>
          isWebMercator(coord) ? webMercatorToLonLat(coord) : coord
        );

        // Ensure the polygon ring is closed
        if (
          geographicRing.length > 0 &&
          (geographicRing[0][0] !==
            geographicRing[geographicRing.length - 1][0] ||
            geographicRing[0][1] !==
              geographicRing[geographicRing.length - 1][1])
        ) {
          geographicRing.push(geographicRing[0]);
        }

        features.features.push({
          type: "Feature",
          properties: { NAME: name, disputed: true },
          geometry: {
            type: "Polygon",
            coordinates: [geographicRing],
          },
        });
      });
    }

    if (regionData.paths) {
      regionData.paths.forEach((path: any) => {
        // Only convert if coordinates are in Web Mercator format
        const geographicPath = path.map((coord: [number, number]) =>
          isWebMercator(coord) ? webMercatorToLonLat(coord) : coord
        );

        features.features.push({
          type: "Feature",
          properties: { NAME: name, disputed: true },
          geometry: {
            type: "LineString",
            coordinates: geographicPath,
          },
        });
      });
    }
  });

  return features;
};
