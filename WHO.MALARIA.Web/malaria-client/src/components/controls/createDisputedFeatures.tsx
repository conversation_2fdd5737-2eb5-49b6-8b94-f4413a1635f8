import { disputedData } from "./disputedMapdata";

// Helper function to determine if coordinates are in Web Mercator format
function isWebMercator([x, y]: [number, number]): boolean {
  // Web Mercator coordinates are typically much larger than longitude/latitude
  // Longitude should be between -180 and 180, latitude between -90 and 90
  return Math.abs(x) > 180 || Math.abs(y) > 90;
}

function webMercatorToLonLat([x, y]: [number, number]): [number, number] {
  const RADIUS = 6378137;

  const lon = (x / RADIUS) * (180 / Math.PI);
  const lat = (y / RADIUS) * (180 / Math.PI);
  return [
    lon,
    (180 / Math.PI) * (2 * Math.atan(Math.exp(y / RADIUS)) - Math.PI / 2),
  ];
}

// Create GeoJSON features from disputedData
export const createDisputedFeatures = () => {
  try {
    const features: any = {
      type: "FeatureCollection",
      features: [],
    };

    console.log(
      "Creating disputed features from data:",
      Object.keys(disputedData)
    );

    // Process regions one by one with individual error handling
    // Revert to working set and add one region at a time
    const safeRegions = [
      "Bline_Kosovo",
      "Gaza",
      "Bahrain",
      "Bline_Sudan", // Test this one first
      "South_sud"    // Test this one next
    ]; // Gradually expanding the safe regions list

    Object.entries(disputedData).forEach(([name, regionData]: any) => {
      // Only process safe regions for now
      if (!safeRegions.includes(name)) {
        console.log(`Skipping region ${name} for now`);
        return;
      }

      try {
        console.log(`Processing region: ${name}`, regionData);

        if (regionData.rings) {
          regionData.rings.forEach((ring: any, ringIndex: number) => {
            try {
              // Validate ring has at least 3 coordinates for a polygon
              if (!Array.isArray(ring) || ring.length < 3) {
                console.warn(`Skipping invalid ring for ${name}:`, ring);
                return;
              }

              // Since coordinates are already in lon/lat format, use them directly
              const geographicRing = ring.map((coord: [number, number]) => {
                if (!Array.isArray(coord) || coord.length !== 2) {
                  console.warn(`Invalid coordinate in ${name}:`, coord);
                  return [0, 0]; // fallback coordinate
                }
                // Validate coordinate ranges
                const [lon, lat] = coord;
                if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
                  console.warn(`Coordinate out of range for ${name}:`, coord);
                  return [0, 0]; // fallback coordinate
                }
                return coord; // Use coordinates as-is since they're already lon/lat
              });

              console.log(`Processing ${name} ring ${ringIndex}:`, {
                originalRing: ring.slice(0, 2),
                convertedRing: geographicRing.slice(0, 2),
                ringLength: ring.length,
              });

              // Ensure the polygon ring is closed
              if (
                geographicRing.length > 0 &&
                (geographicRing[0][0] !==
                  geographicRing[geographicRing.length - 1][0] ||
                  geographicRing[0][1] !==
                    geographicRing[geographicRing.length - 1][1])
              ) {
                geographicRing.push(geographicRing[0]);
              }

              const feature = {
                type: "Feature",
                properties: { NAME: name, disputed: true },
                geometry: {
                  type: "Polygon",
                  coordinates: [geographicRing],
                },
              };

              console.log(`Created feature for ${name}:`, feature);
              features.features.push(feature);
            } catch (error) {
              console.error(`Error processing ring for ${name}:`, error);
            }
          });
        }
        if (regionData.paths) {
          regionData.paths.forEach((path: any, pathIndex: number) => {
            try {
              // Validate path has at least 2 coordinates for a line
              if (!Array.isArray(path) || path.length < 2) {
                console.warn(`Skipping invalid path for ${name}:`, path);
                return;
              }

              // Since coordinates are already in lon/lat format, use them directly
              const geographicPath = path.map((coord: [number, number]) => {
                if (!Array.isArray(coord) || coord.length !== 2) {
                  console.warn(`Invalid coordinate in ${name} path:`, coord);
                  return [0, 0]; // fallback coordinate
                }
                // Validate coordinate ranges
                const [lon, lat] = coord;
                if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
                  console.warn(
                    `Coordinate out of range for ${name} path:`,
                    coord
                  );
                  return [0, 0]; // fallback coordinate
                }
                return coord; // Use coordinates as-is since they're already lon/lat
              });

              console.log(`Processing ${name} path ${pathIndex}:`, {
                originalPath: path.slice(0, 2),
                convertedPath: geographicPath.slice(0, 2),
                pathLength: path.length,
              });

              const feature = {
                type: "Feature",
                properties: { NAME: name, disputed: true },
                geometry: {
                  type: "LineString",
                  coordinates: geographicPath,
                },
              };

              console.log(`Created LineString feature for ${name}:`, feature);
              features.features.push(feature);
            } catch (error) {
              console.error(`Error processing path for ${name}:`, error);
            }
          });
        }
      } catch (error) {
        console.error(`Error processing region ${name}:`, error);
      }
    });

    console.log("Total disputed features created:", features.features.length);
    console.log("Features:", features);
    return features;
  } catch (error) {
    console.error("Error creating disputed features:", error);
    return {
      type: "FeatureCollection",
      features: [],
    };
  }
};
