import { disputedData } from "./disputedMapdata";

// Helper function to determine if coordinates are in Web Mercator format
function isWebMercator([x, y]: [number, number]): boolean {
  // Web Mercator coordinates are typically much larger than longitude/latitude
  // Longitude should be between -180 and 180, latitude between -90 and 90
  return Math.abs(x) > 180 || Math.abs(y) > 90;
}

function webMercatorToLonLat([x, y]: [number, number]): [number, number] {
  const RADIUS = 6378137;

  const lon = (x / RADIUS) * (180 / Math.PI);
  const lat = (y / RADIUS) * (180 / Math.PI);
  return [
    lon,
    (180 / Math.PI) * (2 * Math.atan(Math.exp(y / RADIUS)) - Math.PI / 2),
  ];
}

// Create GeoJSON features from disputedData
export const createDisputedFeatures = () => {
  try {
    const features: any = {
      type: "FeatureCollection",
      features: [],
    };

    console.log(
      "Creating disputed features from data:",
      Object.keys(disputedData)
    );

    Object.entries(disputedData).forEach(([name, regionData]: any) => {
      if (regionData.rings) {
        regionData.rings.forEach((ring: any, ringIndex: number) => {
          try {
            // Validate ring has at least 3 coordinates for a polygon
            if (!Array.isArray(ring) || ring.length < 3) {
              console.warn(`Skipping invalid ring for ${name}:`, ring);
              return;
            }

            // Only convert if coordinates are in Web Mercator format
            const geographicRing = ring.map((coord: [number, number]) => {
              if (!Array.isArray(coord) || coord.length !== 2) {
                console.warn(`Invalid coordinate in ${name}:`, coord);
                return [0, 0]; // fallback coordinate
              }
              return isWebMercator(coord) ? webMercatorToLonLat(coord) : coord;
            });

            console.log(`Processing ${name} ring ${ringIndex}:`, {
              originalRing: ring.slice(0, 2), // First 2 coordinates
              convertedRing: geographicRing.slice(0, 2),
              isWebMercatorDetected: ring.some((coord: [number, number]) =>
                isWebMercator(coord)
              ),
            });

            // Ensure the polygon ring is closed
            if (
              geographicRing.length > 0 &&
              (geographicRing[0][0] !==
                geographicRing[geographicRing.length - 1][0] ||
                geographicRing[0][1] !==
                  geographicRing[geographicRing.length - 1][1])
            ) {
              geographicRing.push(geographicRing[0]);
            }

            features.features.push({
              type: "Feature",
              properties: { NAME: name, disputed: true },
              geometry: {
                type: "Polygon",
                coordinates: [geographicRing],
              },
            });
          } catch (error) {
            console.error(`Error processing ring for ${name}:`, error);
          }
        });
      }

      if (regionData.paths) {
        regionData.paths.forEach((path: any) => {
          try {
            // Only convert if coordinates are in Web Mercator format
            const geographicPath = path.map((coord: [number, number]) =>
              isWebMercator(coord) ? webMercatorToLonLat(coord) : coord
            );

            features.features.push({
              type: "Feature",
              properties: { NAME: name, disputed: true },
              geometry: {
                type: "LineString",
                coordinates: geographicPath,
              },
            });
          } catch (error) {
            console.error(`Error processing path for ${name}:`, error);
          }
        });
      }
    });

    console.log("Total disputed features created:", features.features.length);
    return features;
  } catch (error) {
    console.error("Error creating disputed features:", error);
    return {
      type: "FeatureCollection",
      features: [],
    };
  }
};
